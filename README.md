# Sistema de Gestión de Taller Mecánico

Un sistema web desarrollado en Django para la gestión integral de talleres mecánicos, que permite administrar mecánicos, vehículos y procedimientos de mantenimiento.

## 🚗 Características

- **Gestión de Mecánicos**: Registro y administración de mecánicos con especialidades
- **Gestión de Vehículos**: Control de vehículos con patentes únicas
- **Gestión de Procedimientos**: Seguimiento de trabajos realizados en el taller
- **Panel de Administración**: Interfaz administrativa completa con Django Admin
- **Interfaz Web Responsiva**: Diseño moderno con Bootstrap 5
- **Búsquedas y Filtros**: Funcionalidades avanzadas de búsqueda
- **Datos de Prueba**: Comando para generar datos de ejemplo

## 🛠️ Tecnologías Utilizadas

- **Backend**: Django 4.2.23
- **Base de Datos**: SQLite (desarrollo)
- **Frontend**: HTML5, CSS3, Bootstrap 5, Font Awesome
- **Lenguaje**: Python 3.x

## 📋 Requisitos Previos

- Python 3.8 o superior
- pip (gestor de paquetes de Python)

## 🚀 Instalación y Configuración

### 1. Clonar o descargar el proyecto

```bash
# Si tienes el proyecto en un repositorio
git clone <url-del-repositorio>
cd proyectoDjango

# O simplemente navega al directorio del proyecto
cd proyectoDjango
```

### 2. Crear un entorno virtual (recomendado)

```bash
# Crear entorno virtual
python3 -m venv venv

# Activar entorno virtual
# En macOS/Linux:
source venv/bin/activate
# En Windows:
venv\Scripts\activate
```

### 3. Instalar dependencias

```bash
pip install django
```

### 4. Configurar la base de datos

```bash
# Aplicar migraciones
python3 manage.py migrate
```

### 5. Crear un superusuario (opcional)

```bash
python3 manage.py createsuperuser
```

### 6. Cargar datos de prueba (opcional)

```bash
python3 manage.py crear_datos_prueba
```

Este comando creará:
- 5 mecánicos de ejemplo
- 10 vehículos de ejemplo  
- 20 procedimientos de ejemplo

## 🎯 Ejecución

### Iniciar el servidor de desarrollo

```bash
python3 manage.py runserver
```

El sistema estará disponible en: `http://127.0.0.1:8000/`

### Acceder al panel de administración

Visita: `http://127.0.0.1:8000/admin/`

**Credenciales por defecto** (si usaste el comando de datos de prueba):
- Usuario: `admin`
- Contraseña: `admin123`

## 📱 Uso del Sistema

### Página Principal
- Dashboard con estadísticas generales
- Accesos rápidos a las diferentes secciones
- Lista de procedimientos recientes

### Gestión de Mecánicos
- Lista de todos los mecánicos
- Filtros por especialidad y estado
- Vista detallada con historial de procedimientos

### Gestión de Vehículos
- Lista de vehículos registrados
- Búsqueda por patente, modelo o propietario
- Historial completo de mantenimientos

### Gestión de Procedimientos
- Lista de todos los procedimientos
- Filtros por estado y mecánico
- Seguimiento de costos y fechas

## 🧪 Ejecutar Tests

```bash
python3 manage.py test taller
```

## 📁 Estructura del Proyecto

```
proyectoDjango/
├── taller_mecanico/          # Configuración principal del proyecto
│   ├── settings.py           # Configuraciones
│   ├── urls.py              # URLs principales
│   └── wsgi.py              # Configuración WSGI
├── taller/                   # Aplicación principal
│   ├── models.py            # Modelos de datos
│   ├── views.py             # Vistas
│   ├── admin.py             # Configuración del admin
│   ├── urls.py              # URLs de la aplicación
│   ├── tests.py             # Tests unitarios
│   ├── templates/           # Plantillas HTML
│   ├── static/              # Archivos estáticos (CSS, JS)
│   └── management/          # Comandos personalizados
├── manage.py                # Script de gestión de Django
└── README.md               # Este archivo
```

## 🎨 Modelos de Datos

### Mecánico
- Nombre, contacto, especialidad
- Estado activo/inactivo
- Fecha de ingreso

### Vehículo
- Patente única, modelo, año
- Propietario
- Fecha de registro

### Procedimiento
- Nombre y descripción
- Mecánico asignado
- Vehículo objetivo
- Fechas de inicio y fin
- Costo y estado

## 🔧 Comandos Útiles

```bash
# Crear nuevas migraciones
python3 manage.py makemigrations

# Aplicar migraciones
python3 manage.py migrate

# Crear superusuario
python3 manage.py createsuperuser

# Cargar datos de prueba
python3 manage.py crear_datos_prueba

# Ejecutar tests
python3 manage.py test

# Iniciar servidor
python3 manage.py runserver
```

## 📝 Notas Importantes

- El proyecto usa SQLite como base de datos por defecto
- Los archivos estáticos se sirven automáticamente en modo desarrollo
- El timezone está configurado para Chile (America/Santiago)
- El idioma está configurado en español

## 🤝 Contribución

Este proyecto fue desarrollado como parte de un ejercicio académico siguiendo las mejores prácticas de Django y desarrollo web.

## 📄 Licencia

Este proyecto es de uso educativo y académico.
