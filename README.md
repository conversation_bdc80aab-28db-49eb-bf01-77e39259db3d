# Sistema de Gestión de Taller Mecánico - Backend API

Un sistema backend desarrollado en Django para la gestión integral de talleres mecánicos, que proporciona APIs REST para administrar mecánicos, vehículos y procedimientos de mantenimiento.

## 🚗 Características

- **API REST Completa**: Endpoints JSON para todas las operaciones
- **Gestión de Mecánicos**: Registro y administración de mecánicos con especialidades
- **Gestión de Vehículos**: Control de vehículos con patentes únicas
- **Gestión de Procedimientos**: Seguimiento de trabajos realizados en el taller
- **Panel de Administración**: Interfaz administrativa completa con Django Admin
- **Filtros y Búsquedas**: APIs con funcionalidades avanzadas de filtrado
- **Paginación**: Respuestas paginadas para mejor rendimiento
- **Datos de Prueba**: Comando para generar datos de ejemplo

## 🛠️ Tecnologías Utilizadas

- **Backend**: Django 4.2.23
- **Base de Datos**: SQLite (desarrollo)
- **APIs**: Django REST con JsonResponse
- **Lenguaje**: Python 3.x

## 📋 Requisitos Previos

- Python 3.8 o superior
- pip (gestor de paquetes de Python)

## 🚀 Instalación y Configuración

### 1. Clonar o descargar el proyecto

```bash
# Si tienes el proyecto en un repositorio
git clone <url-del-repositorio>
cd proyectoDjango

# O simplemente navega al directorio del proyecto
cd proyectoDjango
```

### 2. Crear un entorno virtual (recomendado)

```bash
# Crear entorno virtual
python3 -m venv venv

# Activar entorno virtual
# En macOS/Linux:
source venv/bin/activate
# En Windows:
venv\Scripts\activate
```

### 3. Instalar dependencias

```bash
pip install django
```

### 4. Configurar la base de datos

```bash
# Aplicar migraciones
python3 manage.py migrate
```

### 5. Crear un superusuario (opcional)

```bash
python3 manage.py createsuperuser
```

### 6. Cargar datos de prueba (opcional)

```bash
python3 manage.py crear_datos_prueba
```

Este comando creará:
- 5 mecánicos de ejemplo
- 10 vehículos de ejemplo  
- 20 procedimientos de ejemplo

## 🎯 Ejecución

### Iniciar el servidor de desarrollo

```bash
python3 manage.py runserver
```

El sistema estará disponible en: `http://127.0.0.1:8000/`

### Acceder al panel de administración

Visita: `http://127.0.0.1:8000/admin/`

**Credenciales por defecto** (si usaste el comando de datos de prueba):
- Usuario: `admin`
- Contraseña: `admin123`

## 📱 Endpoints de la API

### Estadísticas Generales
- `GET /api/estadisticas/` - Obtiene estadísticas del taller y procedimientos recientes

### APIs de Mecánicos
- `GET /api/mecanicos/` - Lista todos los mecánicos con filtros
  - Parámetros: `especialidad`, `activo`, `page`, `page_size`
- `GET /api/mecanicos/{id}/` - Detalle de un mecánico específico con sus procedimientos

### APIs de Vehículos
- `GET /api/vehiculos/` - Lista todos los vehículos con filtros
  - Parámetros: `search`, `año`, `page`, `page_size`
- `GET /api/vehiculos/{id}/` - Detalle de un vehículo específico con su historial

### APIs de Procedimientos
- `GET /api/procedimientos/` - Lista todos los procedimientos con filtros
  - Parámetros: `completado`, `mecanico`, `search`, `page`, `page_size`
- `GET /api/procedimientos/{id}/` - Detalle de un procedimiento específico

### Ejemplos de Uso
```bash
# Obtener estadísticas
curl http://127.0.0.1:8000/api/estadisticas/

# Listar mecánicos activos
curl http://127.0.0.1:8000/api/mecanicos/?activo=true

# Buscar vehículos por patente
curl http://127.0.0.1:8000/api/vehiculos/?search=ABC123

# Filtrar procedimientos completados
curl http://127.0.0.1:8000/api/procedimientos/?completado=true
```

## 🧪 Ejecutar Tests

```bash
python3 manage.py test taller
```

## 📁 Estructura del Proyecto

```
proyectoDjango/
├── taller_mecanico/          # Configuración principal del proyecto
│   ├── settings.py           # Configuraciones
│   ├── urls.py              # URLs principales
│   └── wsgi.py              # Configuración WSGI
├── taller/                   # Aplicación principal
│   ├── models.py            # Modelos de datos
│   ├── views.py             # Vistas API (JsonResponse)
│   ├── admin.py             # Configuración del admin
│   ├── urls.py              # URLs de la API
│   ├── tests.py             # Tests unitarios
│   └── management/          # Comandos personalizados
├── manage.py                # Script de gestión de Django
└── README.md               # Este archivo
```

## 🎨 Modelos de Datos

### Mecánico
- Nombre, contacto, especialidad
- Estado activo/inactivo
- Fecha de ingreso

### Vehículo
- Patente única, modelo, año
- Propietario
- Fecha de registro

### Procedimiento
- Nombre y descripción
- Mecánico asignado
- Vehículo objetivo
- Fechas de inicio y fin
- Costo y estado

## 🔧 Comandos Útiles

```bash
# Crear nuevas migraciones
python3 manage.py makemigrations

# Aplicar migraciones
python3 manage.py migrate

# Crear superusuario
python3 manage.py createsuperuser

# Cargar datos de prueba
python3 manage.py crear_datos_prueba

# Ejecutar tests
python3 manage.py test

# Iniciar servidor
python3 manage.py runserver
```

## 📝 Notas Importantes

- **Solo Backend**: Este proyecto proporciona únicamente APIs REST, sin frontend
- El proyecto usa SQLite como base de datos por defecto
- Todas las respuestas son en formato JSON
- El timezone está configurado para Chile (America/Santiago)
- El idioma está configurado en español
- Las APIs incluyen paginación automática
- Filtros y búsquedas disponibles en todos los endpoints de listado

## 🤝 Contribución

Este proyecto fue desarrollado como parte de un ejercicio académico siguiendo las mejores prácticas de Django y desarrollo web.

## 📄 Licencia

Este proyecto es de uso educativo y académico.
