from django.contrib import admin
from django.utils.html import format_html
from .models import Mecanico, Veh<PERSON>ulo, Procedimiento


@admin.register(Mecanico)
class MecanicoAdmin(admin.ModelAdmin):
    """
    Configuración del admin para el modelo Mecánico.
    """
    list_display = ['nombre', 'especialidad', 'contacto', 'activo', 'fecha_ingreso']
    list_filter = ['especialidad', 'activo', 'fecha_ingreso']
    search_fields = ['nombre', 'especialidad', 'contacto']
    list_editable = ['activo']
    ordering = ['nombre']
    readonly_fields = ['fecha_ingreso']

    fieldsets = (
        ('Información Personal', {
            'fields': ('nombre', 'contacto')
        }),
        ('Información Profesional', {
            'fields': ('especialidad', 'activo')
        }),
        ('Fechas', {
            'fields': ('fecha_ingreso',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """Optimizar consultas"""
        return super().get_queryset(request).prefetch_related('procedimientos')


@admin.register(Vehiculo)
class VehiculoAdmin(admin.ModelAdmin):
    """
    Configuración del admin para el modelo Vehículo.
    """
    list_display = ['patente', 'modelo', 'año', 'dueño', 'fecha_registro']
    list_filter = ['año', 'fecha_registro']
    search_fields = ['patente', 'modelo', 'dueño']
    ordering = ['patente']
    readonly_fields = ['fecha_registro']

    fieldsets = (
        ('Información del Vehículo', {
            'fields': ('patente', 'modelo', 'año')
        }),
        ('Propietario', {
            'fields': ('dueño',)
        }),
        ('Fechas', {
            'fields': ('fecha_registro',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """Optimizar consultas"""
        return super().get_queryset(request).prefetch_related('procedimientos')


class ProcedimientoInline(admin.TabularInline):
    """
    Inline para mostrar procedimientos en otros modelos.
    """
    model = Procedimiento
    extra = 0
    readonly_fields = ['fecha_inicio']
    fields = ['nombre', 'descripcion', 'costo', 'completado', 'fecha_inicio']


@admin.register(Procedimiento)
class ProcedimientoAdmin(admin.ModelAdmin):
    """
    Configuración del admin para el modelo Procedimiento.
    """
    list_display = [
        'nombre', 'vehiculo', 'mecanico', 'costo',
        'completado', 'fecha_inicio', 'fecha_fin'
    ]
    list_filter = ['completado', 'fecha_inicio', 'mecanico__especialidad']
    search_fields = ['nombre', 'descripcion', 'vehiculo__patente', 'mecanico__nombre']
    list_editable = ['completado']
    ordering = ['-fecha_inicio']
    readonly_fields = ['fecha_inicio']
    date_hierarchy = 'fecha_inicio'

    fieldsets = (
        ('Información del Procedimiento', {
            'fields': ('nombre', 'descripcion')
        }),
        ('Asignación', {
            'fields': ('mecanico', 'vehiculo')
        }),
        ('Fechas y Estado', {
            'fields': ('fecha_inicio', 'fecha_fin', 'completado')
        }),
        ('Costo', {
            'fields': ('costo',)
        }),
    )

    def estado_display(self, obj):
        """Mostrar el estado con colores"""
        if obj.completado:
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ Completado</span>'
            )
        else:
            return format_html(
                '<span style="color: orange; font-weight: bold;">⏳ En proceso</span>'
            )
    estado_display.short_description = 'Estado'

    def get_queryset(self, request):
        """Optimizar consultas con select_related"""
        return super().get_queryset(request).select_related('mecanico', 'vehiculo')


# Personalización del sitio admin
admin.site.site_header = "Administración Taller Mecánico"
admin.site.site_title = "Taller Mecánico"
admin.site.index_title = "Panel de Administración"
