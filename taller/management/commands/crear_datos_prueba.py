from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import random
from taller.models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Procedimiento


class Command(BaseCommand):
    help = 'Crea datos de prueba para el sistema del taller mecánico'

    def add_arguments(self, parser):
        parser.add_argument(
            '--mecanicos',
            type=int,
            default=5,
            help='Número de mecánicos a crear (default: 5)'
        )
        parser.add_argument(
            '--vehiculos',
            type=int,
            default=10,
            help='Número de vehículos a crear (default: 10)'
        )
        parser.add_argument(
            '--procedimientos',
            type=int,
            default=20,
            help='Número de procedimientos a crear (default: 20)'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Iniciando creación de datos de prueba...')
        )

        # Crear mecánicos
        mecanicos_data = [
            {'nombre': '<PERSON>', 'contacto': '+56912345678', 'especialidad': 'Motor'},
            {'nombre': '<PERSON>', 'contacto': '+56987654321', 'especialidad': 'Frenos'},
            {'nombre': 'Carlos Rodríguez', 'contacto': '+56911223344', 'especialidad': 'Transmisión'},
            {'nombre': 'Ana Martínez', 'contacto': '+56955667788', 'especialidad': 'Suspensión'},
            {'nombre': 'Luis Fernández', 'contacto': '+56933445566', 'especialidad': 'Electricidad'},
            {'nombre': 'Carmen Silva', 'contacto': '+56977889900', 'especialidad': 'Carrocería'},
            {'nombre': 'Roberto Díaz', 'contacto': '+56944556677', 'especialidad': 'Aire Acondicionado'},
        ]

        mecanicos_creados = []
        for i in range(options['mecanicos']):
            data = mecanicos_data[i % len(mecanicos_data)]
            mecanico, created = Mecanico.objects.get_or_create(
                nombre=f"{data['nombre']} {i+1}" if i >= len(mecanicos_data) else data['nombre'],
                defaults={
                    'contacto': data['contacto'],
                    'especialidad': data['especialidad'],
                    'activo': random.choice([True, True, True, False])  # 75% activos
                }
            )
            if created:
                mecanicos_creados.append(mecanico)

        self.stdout.write(
            self.style.SUCCESS(f'✓ Creados {len(mecanicos_creados)} mecánicos')
        )

        # Crear vehículos
        patentes = ['ABC123', 'DEF456', 'GHI789', 'JKL012', 'MNO345', 
                   'PQR678', 'STU901', 'VWX234', 'YZA567', 'BCD890',
                   'EFG123', 'HIJ456', 'KLM789', 'NOP012', 'QRS345']
        
        modelos = ['Toyota Corolla', 'Honda Civic', 'Nissan Sentra', 'Chevrolet Cruze',
                  'Ford Focus', 'Hyundai Elantra', 'Kia Rio', 'Mazda 3',
                  'Volkswagen Jetta', 'Peugeot 208', 'Renault Logan']
        
        dueños = ['Pedro Sánchez', 'Laura Morales', 'Diego Herrera', 'Sofía Castro',
                 'Andrés Vargas', 'Valentina Ruiz', 'Sebastián Torres', 'Camila Jiménez',
                 'Mateo Guerrero', 'Isabella Mendoza', 'Santiago Ramos']

        vehiculos_creados = []
        for i in range(options['vehiculos']):
            patente = patentes[i % len(patentes)]
            if i >= len(patentes):
                patente = f"XYZ{100 + i}"
            
            vehiculo, created = Vehiculo.objects.get_or_create(
                patente=patente,
                defaults={
                    'modelo': random.choice(modelos),
                    'año': random.randint(2010, 2024),
                    'dueño': random.choice(dueños)
                }
            )
            if created:
                vehiculos_creados.append(vehiculo)

        self.stdout.write(
            self.style.SUCCESS(f'✓ Creados {len(vehiculos_creados)} vehículos')
        )

        # Crear procedimientos
        procedimientos_nombres = [
            'Cambio de aceite', 'Revisión de frenos', 'Alineación y balanceo',
            'Cambio de filtros', 'Revisión de motor', 'Cambio de neumáticos',
            'Reparación de transmisión', 'Mantenimiento preventivo',
            'Cambio de batería', 'Revisión eléctrica', 'Reparación de suspensión',
            'Cambio de correa de distribución', 'Limpieza de inyectores',
            'Reparación de aire acondicionado', 'Cambio de pastillas de freno'
        ]

        descripciones = [
            'Cambio completo de aceite del motor y filtro',
            'Revisión completa del sistema de frenos y cambio de pastillas',
            'Alineación de ruedas y balanceo de neumáticos',
            'Cambio de filtro de aire, combustible y habitáculo',
            'Diagnóstico completo del motor y reparaciones necesarias',
            'Cambio de neumáticos desgastados por nuevos',
            'Reparación completa de la caja de cambios',
            'Mantenimiento preventivo según kilometraje',
            'Cambio de batería y revisión del sistema eléctrico',
            'Diagnóstico y reparación de fallas eléctricas',
            'Reparación de amortiguadores y sistema de suspensión',
            'Cambio de correa de distribución y componentes relacionados',
            'Limpieza profesional de inyectores de combustible',
            'Reparación y recarga del sistema de aire acondicionado',
            'Cambio de pastillas y discos de freno'
        ]

        procedimientos_creados = []
        mecanicos_disponibles = Mecanico.objects.filter(activo=True)
        vehiculos_disponibles = Vehiculo.objects.all()

        for i in range(options['procedimientos']):
            nombre = random.choice(procedimientos_nombres)
            descripcion = random.choice(descripciones)
            
            # Fechas aleatorias en los últimos 30 días
            dias_atras = random.randint(0, 30)
            fecha_inicio = timezone.now() - timezone.timedelta(days=dias_atras)
            
            completado = random.choice([True, False])
            fecha_fin = None
            if completado:
                fecha_fin = fecha_inicio + timezone.timedelta(
                    hours=random.randint(1, 48)
                )

            procedimiento = Procedimiento.objects.create(
                nombre=nombre,
                descripcion=descripcion,
                mecanico=random.choice(mecanicos_disponibles),
                vehiculo=random.choice(vehiculos_disponibles),
                fecha_inicio=fecha_inicio,
                fecha_fin=fecha_fin,
                costo=Decimal(str(random.randint(15000, 500000))),
                completado=completado
            )
            procedimientos_creados.append(procedimiento)

        self.stdout.write(
            self.style.SUCCESS(f'✓ Creados {len(procedimientos_creados)} procedimientos')
        )

        # Resumen
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('RESUMEN DE DATOS CREADOS:'))
        self.stdout.write(f'• Mecánicos: {len(mecanicos_creados)}')
        self.stdout.write(f'• Vehículos: {len(vehiculos_creados)}')
        self.stdout.write(f'• Procedimientos: {len(procedimientos_creados)}')
        self.stdout.write('='*50)
        self.stdout.write(
            self.style.SUCCESS('¡Datos de prueba creados exitosamente!')
        )
