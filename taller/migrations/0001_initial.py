# Generated by Django 4.2.23 on 2025-08-26 22:55

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Mecanico',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(help_text='Nombre completo del mecánico', max_length=100, validators=[django.core.validators.MinLengthValidator(2)])),
                ('contacto', models.CharField(help_text='Número de teléfono de contacto', max_length=15, validators=[django.core.validators.RegexValidator(message='El número de contacto debe tener entre 9 y 15 dígitos.', regex='^\\+?1?\\d{9,15}$')])),
                ('especialidad', models.Char<PERSON>ield(help_text='Especialidad del mecánico (ej: Motor, Frenos, Transmisión)', max_length=100)),
                ('fecha_ingreso', models.DateTimeField(default=django.utils.timezone.now, help_text='Fecha de ingreso al taller')),
                ('activo', models.BooleanField(default=True, help_text='Indica si el mecánico está activo')),
            ],
            options={
                'verbose_name': 'Mecánico',
                'verbose_name_plural': 'Mecánicos',
                'ordering': ['nombre'],
            },
        ),
        migrations.CreateModel(
            name='Vehiculo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('patente', models.CharField(help_text='Patente única del vehículo', max_length=10, unique=True, validators=[django.core.validators.RegexValidator(message='La patente debe tener el formato correcto (ej: ABC123, ABCD12)', regex='^[A-Z]{2,4}[0-9]{2,4}$')])),
                ('modelo', models.CharField(help_text='Modelo del vehículo', max_length=50)),
                ('año', models.PositiveIntegerField(help_text='Año de fabricación del vehículo')),
                ('dueño', models.CharField(help_text='Nombre del propietario del vehículo', max_length=100, validators=[django.core.validators.MinLengthValidator(2)])),
                ('fecha_registro', models.DateTimeField(default=django.utils.timezone.now, help_text='Fecha de registro en el sistema')),
            ],
            options={
                'verbose_name': 'Vehículo',
                'verbose_name_plural': 'Vehículos',
                'ordering': ['patente'],
            },
        ),
        migrations.CreateModel(
            name='Procedimiento',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(help_text='Nombre del procedimiento', max_length=100)),
                ('descripcion', models.TextField(help_text='Descripción detallada del procedimiento')),
                ('fecha_inicio', models.DateTimeField(default=django.utils.timezone.now, help_text='Fecha y hora de inicio del procedimiento')),
                ('fecha_fin', models.DateTimeField(blank=True, help_text='Fecha y hora de finalización del procedimiento', null=True)),
                ('costo', models.DecimalField(decimal_places=2, default=0.0, help_text='Costo del procedimiento', max_digits=10)),
                ('completado', models.BooleanField(default=False, help_text='Indica si el procedimiento está completado')),
                ('mecanico', models.ForeignKey(help_text='Mecánico que realiza el procedimiento', on_delete=django.db.models.deletion.CASCADE, related_name='procedimientos', to='taller.mecanico')),
                ('vehiculo', models.ForeignKey(help_text='Vehículo sobre el cual se realiza el procedimiento', on_delete=django.db.models.deletion.CASCADE, related_name='procedimientos', to='taller.vehiculo')),
            ],
            options={
                'verbose_name': 'Procedimiento',
                'verbose_name_plural': 'Procedimientos',
                'ordering': ['-fecha_inicio'],
            },
        ),
    ]
