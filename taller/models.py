from django.db import models
from django.core.validators import MinLengthValidator, RegexValidator
from django.utils import timezone


class Mecanico(models.Model):
    """
    Modelo que representa a un mecánico del taller.
    """
    nombre = models.CharField(
        max_length=100,
        validators=[MinLengthValidator(2)],
        help_text="Nombre completo del mecánico"
    )
    contacto = models.CharField(
        max_length=15,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="El número de contacto debe tener entre 9 y 15 dígitos."
            )
        ],
        help_text="Número de teléfono de contacto"
    )
    especialidad = models.CharField(
        max_length=100,
        help_text="Especialidad del mecánico (ej: Motor, Frenos, Transmisión)"
    )
    fecha_ingreso = models.DateTimeField(
        default=timezone.now,
        help_text="Fecha de ingreso al taller"
    )
    activo = models.BooleanField(
        default=True,
        help_text="Indica si el mecánico está activo"
    )

    class Meta:
        verbose_name = "Mecánico"
        verbose_name_plural = "Mecánicos"
        ordering = ['nombre']

    def __str__(self):
        return f"{self.nombre} - {self.especialidad}"


class Vehiculo(models.Model):
    """
    Modelo que representa un vehículo en el taller.
    """
    patente = models.CharField(
        max_length=10,
        unique=True,
        validators=[
            RegexValidator(
                regex=r'^[A-Z]{2,4}[0-9]{2,4}$',
                message="La patente debe tener el formato correcto (ej: ABC123, ABCD12)"
            )
        ],
        help_text="Patente única del vehículo"
    )
    modelo = models.CharField(
        max_length=50,
        help_text="Modelo del vehículo"
    )
    año = models.PositiveIntegerField(
        help_text="Año de fabricación del vehículo"
    )
    dueño = models.CharField(
        max_length=100,
        validators=[MinLengthValidator(2)],
        help_text="Nombre del propietario del vehículo"
    )
    fecha_registro = models.DateTimeField(
        default=timezone.now,
        help_text="Fecha de registro en el sistema"
    )

    class Meta:
        verbose_name = "Vehículo"
        verbose_name_plural = "Vehículos"
        ordering = ['patente']

    def __str__(self):
        return f"{self.patente} - {self.modelo} ({self.año})"

    def clean(self):
        """Validación personalizada para el año del vehículo"""
        from django.core.exceptions import ValidationError
        current_year = timezone.now().year
        if self.año < 1900 or self.año > current_year + 1:
            raise ValidationError(
                f'El año debe estar entre 1900 y {current_year + 1}'
            )


class Procedimiento(models.Model):
    """
    Modelo que representa un procedimiento realizado en el taller.
    """
    nombre = models.CharField(
        max_length=100,
        help_text="Nombre del procedimiento"
    )
    descripcion = models.TextField(
        help_text="Descripción detallada del procedimiento"
    )
    mecanico = models.ForeignKey(
        Mecanico,
        on_delete=models.CASCADE,
        related_name='procedimientos',
        help_text="Mecánico que realiza el procedimiento"
    )
    vehiculo = models.ForeignKey(
        Vehiculo,
        on_delete=models.CASCADE,
        related_name='procedimientos',
        help_text="Vehículo sobre el cual se realiza el procedimiento"
    )
    fecha_inicio = models.DateTimeField(
        default=timezone.now,
        help_text="Fecha y hora de inicio del procedimiento"
    )
    fecha_fin = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Fecha y hora de finalización del procedimiento"
    )
    costo = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Costo del procedimiento"
    )
    completado = models.BooleanField(
        default=False,
        help_text="Indica si el procedimiento está completado"
    )

    class Meta:
        verbose_name = "Procedimiento"
        verbose_name_plural = "Procedimientos"
        ordering = ['-fecha_inicio']

    def __str__(self):
        estado = "Completado" if self.completado else "En proceso"
        return f"{self.nombre} - {self.vehiculo.patente} ({estado})"

    def clean(self):
        """Validación personalizada para las fechas"""
        from django.core.exceptions import ValidationError
        if self.fecha_fin and self.fecha_fin < self.fecha_inicio:
            raise ValidationError(
                'La fecha de fin no puede ser anterior a la fecha de inicio'
            )

    def save(self, *args, **kwargs):
        """Override del método save para validaciones adicionales"""
        self.full_clean()
        super().save(*args, **kwargs)
