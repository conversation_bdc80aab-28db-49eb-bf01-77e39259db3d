/* Estilos personalizados para el Taller Mecánico */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0 !important;
}

.stats-card {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 10px;
}

.stats-card .card-body {
    text-align: center;
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0;
}

.badge-status {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.table th {
    background-color: #343a40;
    color: white;
    border: none;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
}

.pagination .page-link {
    color: #007bff;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-color: #007bff;
}

.search-form {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.detail-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.detail-header {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    padding: 1.5rem;
}

.detail-body {
    padding: 1.5rem;
}

.info-row {
    border-bottom: 1px solid #eee;
    padding: 0.75rem 0;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: bold;
    color: #495057;
}

.status-completed {
    color: #28a745;
    font-weight: bold;
}

.status-pending {
    color: #ffc107;
    font-weight: bold;
}

.footer {
    margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stats-card h3 {
        font-size: 2rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .search-form {
        padding: 1rem;
    }
}
