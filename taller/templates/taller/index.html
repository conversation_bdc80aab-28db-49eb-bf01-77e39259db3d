{% extends 'taller/base.html' %}

{% block title %}Inicio - Taller Mecánico{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-4 text-center mb-4">
            <i class="fas fa-wrench text-primary"></i>
            Bienvenido al Taller Mecánico
        </h1>
        <p class="lead text-center text-muted">
            Sistema de gestión integral para talleres mecánicos
        </p>
    </div>
</div>

<!-- Estadísticas -->
<div class="row mb-5">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3>{{ total_mecanicos }}</h3>
                <p class="mb-0">Mecánicos Activos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <i class="fas fa-car fa-2x mb-2"></i>
                <h3>{{ total_vehiculos }}</h3>
                <p class="mb-0">Vehículos Registrados</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <h3>{{ procedimientos_activos }}</h3>
                <p class="mb-0">En Proceso</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h3>{{ procedimientos_completados }}</h3>
                <p class="mb-0">Completados</p>
            </div>
        </div>
    </div>
</div>

<!-- Accesos rápidos -->
<div class="row mb-5">
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> Mecánicos
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">Gestiona la información de los mecánicos del taller.</p>
                <a href="{% url 'taller:mecanicos_list' %}" class="btn btn-primary">
                    <i class="fas fa-eye"></i> Ver Mecánicos
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-car"></i> Vehículos
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">Administra los vehículos que ingresan al taller.</p>
                <a href="{% url 'taller:vehiculos_list' %}" class="btn btn-success">
                    <i class="fas fa-eye"></i> Ver Vehículos
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools"></i> Procedimientos
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">Controla todos los procedimientos del taller.</p>
                <a href="{% url 'taller:procedimientos_list' %}" class="btn btn-warning">
                    <i class="fas fa-eye"></i> Ver Procedimientos
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Procedimientos recientes -->
{% if procedimientos_recientes %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Procedimientos Recientes
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Procedimiento</th>
                                <th>Vehículo</th>
                                <th>Mecánico</th>
                                <th>Estado</th>
                                <th>Fecha</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for procedimiento in procedimientos_recientes %}
                            <tr>
                                <td>
                                    <a href="{% url 'taller:procedimiento_detail' procedimiento.pk %}">
                                        {{ procedimiento.nombre }}
                                    </a>
                                </td>
                                <td>
                                    <a href="{% url 'taller:vehiculo_detail' procedimiento.vehiculo.pk %}">
                                        {{ procedimiento.vehiculo.patente }}
                                    </a>
                                </td>
                                <td>
                                    <a href="{% url 'taller:mecanico_detail' procedimiento.mecanico.pk %}">
                                        {{ procedimiento.mecanico.nombre }}
                                    </a>
                                </td>
                                <td>
                                    {% if procedimiento.completado %}
                                        <span class="badge bg-success">Completado</span>
                                    {% else %}
                                        <span class="badge bg-warning">En proceso</span>
                                    {% endif %}
                                </td>
                                <td>{{ procedimiento.fecha_inicio|date:"d/m/Y H:i" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
