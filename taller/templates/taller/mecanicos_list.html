{% extends 'taller/base.html' %}

{% block title %}Mecánicos - Taller Mecánico{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1>
            <i class="fas fa-users text-primary"></i>
            Mecánicos del Taller
        </h1>
    </div>
</div>

<!-- Formulario de filtros -->
<div class="search-form">
    <form method="get" class="row g-3">
        <div class="col-md-4">
            <label for="especialidad" class="form-label">Especialidad</label>
            <input type="text" class="form-control" id="especialidad" name="especialidad" 
                   value="{{ especialidad }}" placeholder="Buscar por especialidad">
        </div>
        <div class="col-md-4">
            <label for="activo" class="form-label">Estado</label>
            <select class="form-select" id="activo" name="activo">
                <option value="">Todos</option>
                <option value="true" {% if activo == 'true' %}selected{% endif %}>Activos</option>
                <option value="false" {% if activo == 'false' %}selected{% endif %}>Inactivos</option>
            </select>
        </div>
        <div class="col-md-4 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search"></i> Buscar
            </button>
            <a href="{% url 'taller:mecanicos_list' %}" class="btn btn-secondary">
                <i class="fas fa-times"></i> Limpiar
            </a>
        </div>
    </form>
</div>

<!-- Lista de mecánicos -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Lista de Mecánicos
            <span class="badge bg-primary ms-2">{{ page_obj.paginator.count }} total</span>
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Nombre</th>
                            <th>Especialidad</th>
                            <th>Contacto</th>
                            <th>Estado</th>
                            <th>Procedimientos</th>
                            <th>Fecha Ingreso</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for mecanico in page_obj %}
                        <tr>
                            <td>
                                <strong>{{ mecanico.nombre }}</strong>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ mecanico.especialidad }}</span>
                            </td>
                            <td>
                                <i class="fas fa-phone"></i> {{ mecanico.contacto }}
                            </td>
                            <td>
                                {% if mecanico.activo %}
                                    <span class="badge bg-success">Activo</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactivo</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ mecanico.total_procedimientos }}</span>
                            </td>
                            <td>{{ mecanico.fecha_ingreso|date:"d/m/Y" }}</td>
                            <td>
                                <a href="{% url 'taller:mecanico_detail' mecanico.pk %}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> Ver
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Paginación -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Paginación de mecánicos">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if especialidad %}&especialidad={{ especialidad }}{% endif %}{% if activo %}&activo={{ activo }}{% endif %}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if especialidad %}&especialidad={{ especialidad }}{% endif %}{% if activo %}&activo={{ activo }}{% endif %}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if especialidad %}&especialidad={{ especialidad }}{% endif %}{% if activo %}&activo={{ activo }}{% endif %}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if especialidad %}&especialidad={{ especialidad }}{% endif %}{% if activo %}&activo={{ activo }}{% endif %}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No se encontraron mecánicos</h4>
                <p class="text-muted">No hay mecánicos que coincidan con los criterios de búsqueda.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
