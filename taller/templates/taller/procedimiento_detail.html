{% extends 'taller/base.html' %}

{% block title %}{{ procedimiento.nombre }} - Procedimiento{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'taller:index' %}">Inicio</a></li>
                <li class="breadcrumb-item"><a href="{% url 'taller:procedimientos_list' %}">Procedimientos</a></li>
                <li class="breadcrumb-item active">{{ procedimiento.nombre }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <!-- Información del procedimiento -->
    <div class="col-md-6 mb-4">
        <div class="detail-card">
            <div class="detail-header">
                <h4 class="mb-0">
                    <i class="fas fa-tools"></i> {{ procedimiento.nombre }}
                </h4>
            </div>
            <div class="detail-body">
                <div class="info-row">
                    <div class="info-label">Descripción:</div>
                    <div>{{ procedimiento.descripcion }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Estado:</div>
                    <div>
                        {% if procedimiento.completado %}
                            <span class="badge bg-success">
                                <i class="fas fa-check"></i> Completado
                            </span>
                        {% else %}
                            <span class="badge bg-warning">
                                <i class="fas fa-clock"></i> En proceso
                            </span>
                        {% endif %}
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-label">Costo:</div>
                    <div><strong class="text-success">${{ procedimiento.costo|floatformat:0 }}</strong></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Fecha de Inicio:</div>
                    <div>{{ procedimiento.fecha_inicio|date:"d/m/Y H:i" }}</div>
                </div>
                {% if procedimiento.fecha_fin %}
                <div class="info-row">
                    <div class="info-label">Fecha de Finalización:</div>
                    <div>{{ procedimiento.fecha_fin|date:"d/m/Y H:i" }}</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Información del vehículo y mecánico -->
    <div class="col-md-6 mb-4">
        <!-- Vehículo -->
        <div class="card mb-3">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-car"></i> Vehículo
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <strong>Patente:</strong><br>
                        <a href="{% url 'taller:vehiculo_detail' procedimiento.vehiculo.pk %}" class="text-primary">
                            {{ procedimiento.vehiculo.patente }}
                        </a>
                    </div>
                    <div class="col-6">
                        <strong>Modelo:</strong><br>
                        {{ procedimiento.vehiculo.modelo }}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <strong>Año:</strong><br>
                        <span class="badge bg-secondary">{{ procedimiento.vehiculo.año }}</span>
                    </div>
                    <div class="col-6">
                        <strong>Propietario:</strong><br>
                        <i class="fas fa-user"></i> {{ procedimiento.vehiculo.dueño }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Mecánico -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-user"></i> Mecánico Asignado
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <strong>Nombre:</strong><br>
                        <a href="{% url 'taller:mecanico_detail' procedimiento.mecanico.pk %}" class="text-primary">
                            {{ procedimiento.mecanico.nombre }}
                        </a>
                    </div>
                    <div class="col-6">
                        <strong>Especialidad:</strong><br>
                        <span class="badge bg-info">{{ procedimiento.mecanico.especialidad }}</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <strong>Contacto:</strong><br>
                        <i class="fas fa-phone"></i> {{ procedimiento.mecanico.contacto }}
                    </div>
                    <div class="col-6">
                        <strong>Estado:</strong><br>
                        {% if procedimiento.mecanico.activo %}
                            <span class="badge bg-success">Activo</span>
                        {% else %}
                            <span class="badge bg-secondary">Inactivo</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Información adicional -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Información Adicional
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-calendar-alt fa-2x text-primary mb-2"></i>
                            <h6>Duración</h6>
                            {% if procedimiento.fecha_fin %}
                                <p class="mb-0">
                                    {% with duracion=procedimiento.fecha_fin|timeuntil:procedimiento.fecha_inicio %}
                                        {{ duracion }}
                                    {% endwith %}
                                </p>
                            {% else %}
                                <p class="mb-0 text-warning">En proceso</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                            <h6>Costo Total</h6>
                            <p class="mb-0 h5 text-success">${{ procedimiento.costo|floatformat:0 }}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-clipboard-check fa-2x text-info mb-2"></i>
                            <h6>Estado Actual</h6>
                            {% if procedimiento.completado %}
                                <p class="mb-0 text-success">
                                    <i class="fas fa-check-circle"></i> Completado
                                </p>
                            {% else %}
                                <p class="mb-0 text-warning">
                                    <i class="fas fa-clock"></i> En proceso
                                </p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
