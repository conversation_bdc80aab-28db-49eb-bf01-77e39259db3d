{% extends 'taller/base.html' %}

{% block title %}Procedimientos - Taller Mecánico{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1>
            <i class="fas fa-tools text-warning"></i>
            Procedimientos del Taller
        </h1>
    </div>
</div>

<!-- Formulario de filtros -->
<div class="search-form">
    <form method="get" class="row g-3">
        <div class="col-md-3">
            <label for="search" class="form-label">Buscar</label>
            <input type="text" class="form-control" id="search" name="search" 
                   value="{{ search }}" placeholder="Nombre, descripción o patente">
        </div>
        <div class="col-md-3">
            <label for="completado" class="form-label">Estado</label>
            <select class="form-select" id="completado" name="completado">
                <option value="">Todos</option>
                <option value="false" {% if completado == 'false' %}selected{% endif %}>En proceso</option>
                <option value="true" {% if completado == 'true' %}selected{% endif %}>Completados</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="mecanico" class="form-label">Mecánico</label>
            <select class="form-select" id="mecanico" name="mecanico">
                <option value="">Todos</option>
                {% for mec in mecanicos %}
                    <option value="{{ mec.pk }}" {% if mecanico_id == mec.pk|stringformat:"s" %}selected{% endif %}>
                        {{ mec.nombre }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-warning me-2">
                <i class="fas fa-search"></i> Buscar
            </button>
            <a href="{% url 'taller:procedimientos_list' %}" class="btn btn-secondary">
                <i class="fas fa-times"></i> Limpiar
            </a>
        </div>
    </form>
</div>

<!-- Lista de procedimientos -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Lista de Procedimientos
            <span class="badge bg-warning ms-2">{{ page_obj.paginator.count }} total</span>
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Procedimiento</th>
                            <th>Vehículo</th>
                            <th>Mecánico</th>
                            <th>Estado</th>
                            <th>Costo</th>
                            <th>Fecha Inicio</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for procedimiento in page_obj %}
                        <tr>
                            <td>
                                <strong>{{ procedimiento.nombre }}</strong>
                                <br>
                                <small class="text-muted">{{ procedimiento.descripcion|truncatechars:40 }}</small>
                            </td>
                            <td>
                                <a href="{% url 'taller:vehiculo_detail' procedimiento.vehiculo.pk %}">
                                    <strong class="text-primary">{{ procedimiento.vehiculo.patente }}</strong>
                                </a>
                                <br>
                                <small class="text-muted">{{ procedimiento.vehiculo.modelo }}</small>
                            </td>
                            <td>
                                <a href="{% url 'taller:mecanico_detail' procedimiento.mecanico.pk %}">
                                    {{ procedimiento.mecanico.nombre }}
                                </a>
                                <br>
                                <small class="text-muted">{{ procedimiento.mecanico.especialidad }}</small>
                            </td>
                            <td>
                                {% if procedimiento.completado %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> Completado
                                    </span>
                                {% else %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock"></i> En proceso
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <strong>${{ procedimiento.costo|floatformat:0 }}</strong>
                            </td>
                            <td>
                                {{ procedimiento.fecha_inicio|date:"d/m/Y" }}
                                <br>
                                <small class="text-muted">{{ procedimiento.fecha_inicio|date:"H:i" }}</small>
                            </td>
                            <td>
                                <a href="{% url 'taller:procedimiento_detail' procedimiento.pk %}" 
                                   class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-eye"></i> Ver
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Paginación -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Paginación de procedimientos">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if completado %}&completado={{ completado }}{% endif %}{% if mecanico_id %}&mecanico={{ mecanico_id }}{% endif %}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if completado %}&completado={{ completado }}{% endif %}{% if mecanico_id %}&mecanico={{ mecanico_id }}{% endif %}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if completado %}&completado={{ completado }}{% endif %}{% if mecanico_id %}&mecanico={{ mecanico_id }}{% endif %}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if completado %}&completado={{ completado }}{% endif %}{% if mecanico_id %}&mecanico={{ mecanico_id }}{% endif %}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No se encontraron procedimientos</h4>
                <p class="text-muted">No hay procedimientos que coincidan con los criterios de búsqueda.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
