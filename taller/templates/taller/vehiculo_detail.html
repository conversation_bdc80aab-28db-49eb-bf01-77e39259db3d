{% extends 'taller/base.html' %}

{% block title %}{{ vehiculo.patente }} - Vehículo{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'taller:index' %}">Inicio</a></li>
                <li class="breadcrumb-item"><a href="{% url 'taller:vehiculos_list' %}">Vehículos</a></li>
                <li class="breadcrumb-item active">{{ vehiculo.patente }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <!-- Información del vehículo -->
    <div class="col-md-4 mb-4">
        <div class="detail-card">
            <div class="detail-header">
                <h4 class="mb-0">
                    <i class="fas fa-car"></i> {{ vehiculo.patente }}
                </h4>
            </div>
            <div class="detail-body">
                <div class="info-row">
                    <div class="info-label">Modelo:</div>
                    <div><strong>{{ vehiculo.modelo }}</strong></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Año:</div>
                    <div><span class="badge bg-secondary">{{ vehiculo.año }}</span></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Propietario:</div>
                    <div><i class="fas fa-user"></i> {{ vehiculo.dueño }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Fecha de Registro:</div>
                    <div>{{ vehiculo.fecha_registro|date:"d/m/Y" }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Total Procedimientos:</div>
                    <div><span class="badge bg-primary">{{ page_obj.paginator.count }}</span></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Procedimientos del vehículo -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools"></i> Historial de Procedimientos
                </h5>
            </div>
            <div class="card-body">
                {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Procedimiento</th>
                                    <th>Mecánico</th>
                                    <th>Estado</th>
                                    <th>Costo</th>
                                    <th>Fecha Inicio</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for procedimiento in page_obj %}
                                <tr>
                                    <td>
                                        <strong>{{ procedimiento.nombre }}</strong>
                                        <br>
                                        <small class="text-muted">{{ procedimiento.descripcion|truncatechars:50 }}</small>
                                    </td>
                                    <td>
                                        <a href="{% url 'taller:mecanico_detail' procedimiento.mecanico.pk %}">
                                            {{ procedimiento.mecanico.nombre }}
                                        </a>
                                        <br>
                                        <small class="text-muted">{{ procedimiento.mecanico.especialidad }}</small>
                                    </td>
                                    <td>
                                        {% if procedimiento.completado %}
                                            <span class="badge bg-success">Completado</span>
                                        {% else %}
                                            <span class="badge bg-warning">En proceso</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>${{ procedimiento.costo|floatformat:0 }}</strong>
                                    </td>
                                    <td>{{ procedimiento.fecha_inicio|date:"d/m/Y H:i" }}</td>
                                    <td>
                                        <a href="{% url 'taller:procedimiento_detail' procedimiento.pk %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Paginación -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Paginación de procedimientos">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Sin procedimientos</h5>
                        <p class="text-muted">Este vehículo aún no tiene procedimientos registrados.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
