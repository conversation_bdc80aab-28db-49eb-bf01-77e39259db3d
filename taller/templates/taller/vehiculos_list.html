{% extends 'taller/base.html' %}

{% block title %}Vehículos - Taller Mecánico{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1>
            <i class="fas fa-car text-success"></i>
            Vehículos Registrados
        </h1>
    </div>
</div>

<!-- Formulario de filtros -->
<div class="search-form">
    <form method="get" class="row g-3">
        <div class="col-md-4">
            <label for="search" class="form-label">Buscar</label>
            <input type="text" class="form-control" id="search" name="search" 
                   value="{{ search }}" placeholder="Patente, dueño o modelo">
        </div>
        <div class="col-md-4">
            <label for="año" class="form-label">Año</label>
            <input type="number" class="form-control" id="año" name="año" 
                   value="{{ año }}" placeholder="Año del vehículo">
        </div>
        <div class="col-md-4 d-flex align-items-end">
            <button type="submit" class="btn btn-success me-2">
                <i class="fas fa-search"></i> Buscar
            </button>
            <a href="{% url 'taller:vehiculos_list' %}" class="btn btn-secondary">
                <i class="fas fa-times"></i> Limpiar
            </a>
        </div>
    </form>
</div>

<!-- Lista de vehículos -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Lista de Vehículos
            <span class="badge bg-success ms-2">{{ page_obj.paginator.count }} total</span>
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Patente</th>
                            <th>Modelo</th>
                            <th>Año</th>
                            <th>Dueño</th>
                            <th>Procedimientos</th>
                            <th>Fecha Registro</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for vehiculo in page_obj %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ vehiculo.patente }}</strong>
                            </td>
                            <td>{{ vehiculo.modelo }}</td>
                            <td>
                                <span class="badge bg-secondary">{{ vehiculo.año }}</span>
                            </td>
                            <td>
                                <i class="fas fa-user"></i> {{ vehiculo.dueño }}
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ vehiculo.total_procedimientos }}</span>
                            </td>
                            <td>{{ vehiculo.fecha_registro|date:"d/m/Y" }}</td>
                            <td>
                                <a href="{% url 'taller:vehiculo_detail' vehiculo.pk %}" 
                                   class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-eye"></i> Ver
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Paginación -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Paginación de vehículos">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if año %}&año={{ año }}{% endif %}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if año %}&año={{ año }}{% endif %}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if año %}&año={{ año }}{% endif %}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if año %}&año={{ año }}{% endif %}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-car fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No se encontraron vehículos</h4>
                <p class="text-muted">No hay vehículos que coincidan con los criterios de búsqueda.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
