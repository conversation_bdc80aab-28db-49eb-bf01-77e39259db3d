from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from .models import Mecanico, Vehiculo, Procedimiento


class MecanicoModelTest(TestCase):
    """Tests para el modelo Mecánico"""

    def setUp(self):
        self.mecanico = Mecanico.objects.create(
            nombre="Juan Pérez",
            contacto="+56912345678",
            especialidad="Motor"
        )

    def test_mecanico_creation(self):
        """Test de creación de mecánico"""
        self.assertEqual(self.mecanico.nombre, "<PERSON>")
        self.assertEqual(self.mecanico.contacto, "+56912345678")
        self.assertEqual(self.mecanico.especialidad, "Motor")
        self.assertTrue(self.mecanico.activo)

    def test_mecanico_str(self):
        """Test del método __str__ del mecánico"""
        expected = "<PERSON>"
        self.assertEqual(str(self.mecanico), expected)


class VehiculoModelTest(TestCase):
    """Tests para el modelo Vehículo"""

    def setUp(self):
        self.vehiculo = Vehiculo.objects.create(
            patente="ABC123",
            modelo="Toyota Corolla",
            año=2020,
            dueño="Pedro Sánchez"
        )

    def test_vehiculo_creation(self):
        """Test de creación de vehículo"""
        self.assertEqual(self.vehiculo.patente, "ABC123")
        self.assertEqual(self.vehiculo.modelo, "Toyota Corolla")
        self.assertEqual(self.vehiculo.año, 2020)
        self.assertEqual(self.vehiculo.dueño, "Pedro Sánchez")

    def test_vehiculo_str(self):
        """Test del método __str__ del vehículo"""
        expected = "ABC123 - Toyota Corolla (2020)"
        self.assertEqual(str(self.vehiculo), expected)

    def test_patente_unique(self):
        """Test de que la patente sea única"""
        with self.assertRaises(Exception):
            Vehiculo.objects.create(
                patente="ABC123",  # Patente duplicada
                modelo="Honda Civic",
                año=2021,
                dueño="María González"
            )


class ProcedimientoModelTest(TestCase):
    """Tests para el modelo Procedimiento"""

    def setUp(self):
        self.mecanico = Mecanico.objects.create(
            nombre="Juan Pérez",
            contacto="+56912345678",
            especialidad="Motor"
        )
        self.vehiculo = Vehiculo.objects.create(
            patente="ABC123",
            modelo="Toyota Corolla",
            año=2020,
            dueño="Pedro Sánchez"
        )
        self.procedimiento = Procedimiento.objects.create(
            nombre="Cambio de aceite",
            descripcion="Cambio completo de aceite del motor",
            mecanico=self.mecanico,
            vehiculo=self.vehiculo,
            costo=Decimal('50000.00')
        )

    def test_procedimiento_creation(self):
        """Test de creación de procedimiento"""
        self.assertEqual(self.procedimiento.nombre, "Cambio de aceite")
        self.assertEqual(self.procedimiento.mecanico, self.mecanico)
        self.assertEqual(self.procedimiento.vehiculo, self.vehiculo)
        self.assertEqual(self.procedimiento.costo, Decimal('50000.00'))
        self.assertFalse(self.procedimiento.completado)

    def test_procedimiento_str(self):
        """Test del método __str__ del procedimiento"""
        expected = "Cambio de aceite - ABC123 (En proceso)"
        self.assertEqual(str(self.procedimiento), expected)


class ViewsTest(TestCase):
    """Tests para las vistas"""

    def setUp(self):
        self.client = Client()
        self.mecanico = Mecanico.objects.create(
            nombre="Juan Pérez",
            contacto="+56912345678",
            especialidad="Motor"
        )
        self.vehiculo = Vehiculo.objects.create(
            patente="ABC123",
            modelo="Toyota Corolla",
            año=2020,
            dueño="Pedro Sánchez"
        )
        self.procedimiento = Procedimiento.objects.create(
            nombre="Cambio de aceite",
            descripcion="Cambio completo de aceite del motor",
            mecanico=self.mecanico,
            vehiculo=self.vehiculo,
            costo=Decimal('50000.00')
        )

    def test_index_view(self):
        """Test de la vista principal"""
        response = self.client.get(reverse('taller:index'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Bienvenido al Taller Mecánico")

    def test_mecanicos_list_view(self):
        """Test de la vista de lista de mecánicos"""
        response = self.client.get(reverse('taller:mecanicos_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Juan Pérez")

    def test_vehiculos_list_view(self):
        """Test de la vista de lista de vehículos"""
        response = self.client.get(reverse('taller:vehiculos_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "ABC123")

    def test_procedimientos_list_view(self):
        """Test de la vista de lista de procedimientos"""
        response = self.client.get(reverse('taller:procedimientos_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Cambio de aceite")

    def test_mecanico_detail_view(self):
        """Test de la vista de detalle de mecánico"""
        response = self.client.get(
            reverse('taller:mecanico_detail', kwargs={'pk': self.mecanico.pk})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Juan Pérez")

    def test_vehiculo_detail_view(self):
        """Test de la vista de detalle de vehículo"""
        response = self.client.get(
            reverse('taller:vehiculo_detail', kwargs={'pk': self.vehiculo.pk})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "ABC123")

    def test_procedimiento_detail_view(self):
        """Test de la vista de detalle de procedimiento"""
        response = self.client.get(
            reverse('taller:procedimiento_detail', kwargs={'pk': self.procedimiento.pk})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Cambio de aceite")
