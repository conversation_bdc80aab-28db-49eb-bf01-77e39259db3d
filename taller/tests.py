from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from .models import Mecanico, Vehiculo, Procedimiento


class MecanicoModelTest(TestCase):
    """Tests para el modelo Mecánico"""

    def setUp(self):
        self.mecanico = Mecanico.objects.create(
            nombre="Juan Pérez",
            contacto="+56912345678",
            especialidad="Motor"
        )

    def test_mecanico_creation(self):
        """Test de creación de mecánico"""
        self.assertEqual(self.mecanico.nombre, "<PERSON>")
        self.assertEqual(self.mecanico.contacto, "+56912345678")
        self.assertEqual(self.mecanico.especialidad, "Motor")
        self.assertTrue(self.mecanico.activo)

    def test_mecanico_str(self):
        """Test del método __str__ del mecánico"""
        expected = "<PERSON>"
        self.assertEqual(str(self.mecanico), expected)


class VehiculoModelTest(TestCase):
    """Tests para el modelo Vehículo"""

    def setUp(self):
        self.vehiculo = Vehiculo.objects.create(
            patente="ABC123",
            modelo="Toyota Corolla",
            año=2020,
            dueño="Pedro Sánchez"
        )

    def test_vehiculo_creation(self):
        """Test de creación de vehículo"""
        self.assertEqual(self.vehiculo.patente, "ABC123")
        self.assertEqual(self.vehiculo.modelo, "Toyota Corolla")
        self.assertEqual(self.vehiculo.año, 2020)
        self.assertEqual(self.vehiculo.dueño, "Pedro Sánchez")

    def test_vehiculo_str(self):
        """Test del método __str__ del vehículo"""
        expected = "ABC123 - Toyota Corolla (2020)"
        self.assertEqual(str(self.vehiculo), expected)

    def test_patente_unique(self):
        """Test de que la patente sea única"""
        with self.assertRaises(Exception):
            Vehiculo.objects.create(
                patente="ABC123",  # Patente duplicada
                modelo="Honda Civic",
                año=2021,
                dueño="María González"
            )


class ProcedimientoModelTest(TestCase):
    """Tests para el modelo Procedimiento"""

    def setUp(self):
        self.mecanico = Mecanico.objects.create(
            nombre="Juan Pérez",
            contacto="+56912345678",
            especialidad="Motor"
        )
        self.vehiculo = Vehiculo.objects.create(
            patente="ABC123",
            modelo="Toyota Corolla",
            año=2020,
            dueño="Pedro Sánchez"
        )
        self.procedimiento = Procedimiento.objects.create(
            nombre="Cambio de aceite",
            descripcion="Cambio completo de aceite del motor",
            mecanico=self.mecanico,
            vehiculo=self.vehiculo,
            costo=Decimal('50000.00')
        )

    def test_procedimiento_creation(self):
        """Test de creación de procedimiento"""
        self.assertEqual(self.procedimiento.nombre, "Cambio de aceite")
        self.assertEqual(self.procedimiento.mecanico, self.mecanico)
        self.assertEqual(self.procedimiento.vehiculo, self.vehiculo)
        self.assertEqual(self.procedimiento.costo, Decimal('50000.00'))
        self.assertFalse(self.procedimiento.completado)

    def test_procedimiento_str(self):
        """Test del método __str__ del procedimiento"""
        expected = "Cambio de aceite - ABC123 (En proceso)"
        self.assertEqual(str(self.procedimiento), expected)


class APIViewsTest(TestCase):
    """Tests para las APIs"""

    def setUp(self):
        self.client = Client()
        self.mecanico = Mecanico.objects.create(
            nombre="Juan Pérez",
            contacto="+56912345678",
            especialidad="Motor"
        )
        self.vehiculo = Vehiculo.objects.create(
            patente="ABC123",
            modelo="Toyota Corolla",
            año=2020,
            dueño="Pedro Sánchez"
        )
        self.procedimiento = Procedimiento.objects.create(
            nombre="Cambio de aceite",
            descripcion="Cambio completo de aceite del motor",
            mecanico=self.mecanico,
            vehiculo=self.vehiculo,
            costo=Decimal('50000.00')
        )

    def test_api_estadisticas(self):
        """Test de la API de estadísticas"""
        response = self.client.get(reverse('taller:api_estadisticas'))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('estadisticas', data)
        self.assertIn('procedimientos_recientes', data)
        self.assertEqual(data['estadisticas']['total_mecanicos'], 1)
        self.assertEqual(data['estadisticas']['total_vehiculos'], 1)

    def test_api_mecanicos_list(self):
        """Test de la API de lista de mecánicos"""
        response = self.client.get(reverse('taller:api_mecanicos_list'))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('mecanicos', data)
        self.assertIn('pagination', data)
        self.assertEqual(len(data['mecanicos']), 1)
        self.assertEqual(data['mecanicos'][0]['nombre'], "Juan Pérez")

    def test_api_vehiculos_list(self):
        """Test de la API de lista de vehículos"""
        response = self.client.get(reverse('taller:api_vehiculos_list'))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('vehiculos', data)
        self.assertIn('pagination', data)
        self.assertEqual(len(data['vehiculos']), 1)
        self.assertEqual(data['vehiculos'][0]['patente'], "ABC123")

    def test_api_procedimientos_list(self):
        """Test de la API de lista de procedimientos"""
        response = self.client.get(reverse('taller:api_procedimientos_list'))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('procedimientos', data)
        self.assertIn('pagination', data)
        self.assertEqual(len(data['procedimientos']), 1)
        self.assertEqual(data['procedimientos'][0]['nombre'], "Cambio de aceite")

    def test_api_mecanico_detail(self):
        """Test de la API de detalle de mecánico"""
        response = self.client.get(
            reverse('taller:api_mecanico_detail', kwargs={'pk': self.mecanico.pk})
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('mecanico', data)
        self.assertIn('procedimientos', data)
        self.assertEqual(data['mecanico']['nombre'], "Juan Pérez")

    def test_api_vehiculo_detail(self):
        """Test de la API de detalle de vehículo"""
        response = self.client.get(
            reverse('taller:api_vehiculo_detail', kwargs={'pk': self.vehiculo.pk})
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('vehiculo', data)
        self.assertIn('procedimientos', data)
        self.assertEqual(data['vehiculo']['patente'], "ABC123")

    def test_api_procedimiento_detail(self):
        """Test de la API de detalle de procedimiento"""
        response = self.client.get(
            reverse('taller:api_procedimiento_detail', kwargs={'pk': self.procedimiento.pk})
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('procedimiento', data)
        self.assertEqual(data['procedimiento']['nombre'], "Cambio de aceite")

    def test_api_filtros_mecanicos(self):
        """Test de filtros en API de mecánicos"""
        response = self.client.get(reverse('taller:api_mecanicos_list') + '?especialidad=Motor')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data['mecanicos']), 1)

        response = self.client.get(reverse('taller:api_mecanicos_list') + '?especialidad=Frenos')
        data = response.json()
        self.assertEqual(len(data['mecanicos']), 0)

    def test_api_filtros_vehiculos(self):
        """Test de filtros en API de vehículos"""
        response = self.client.get(reverse('taller:api_vehiculos_list') + '?search=ABC123')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data['vehiculos']), 1)

        response = self.client.get(reverse('taller:api_vehiculos_list') + '?search=XYZ999')
        data = response.json()
        self.assertEqual(len(data['vehiculos']), 0)

    def test_api_filtros_procedimientos(self):
        """Test de filtros en API de procedimientos"""
        response = self.client.get(reverse('taller:api_procedimientos_list') + '?completado=false')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data['procedimientos']), 1)

        response = self.client.get(reverse('taller:api_procedimientos_list') + '?completado=true')
        data = response.json()
        self.assertEqual(len(data['procedimientos']), 0)
