from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.db.models import Count, Q
from django.core.paginator import Paginator
from .models import <PERSON><PERSON><PERSON>, <PERSON>ehiculo, Procedimiento


def index(request):
    """
    Vista principal del taller que muestra estadísticas generales.
    """
    # Estadísticas generales
    total_mecanicos = Mecanico.objects.filter(activo=True).count()
    total_vehiculos = Vehiculo.objects.count()
    procedimientos_activos = Procedimiento.objects.filter(completado=False).count()
    procedimientos_completados = Procedimiento.objects.filter(completado=True).count()

    # Procedimientos recientes
    procedimientos_recientes = Procedimiento.objects.select_related(
        'mecanico', 'vehiculo'
    ).order_by('-fecha_inicio')[:5]

    context = {
        'total_mecanicos': total_mecanicos,
        'total_vehiculos': total_vehiculos,
        'procedimientos_activos': procedimientos_activos,
        'procedimientos_completados': procedimientos_completados,
        'procedimientos_recientes': procedimientos_recientes,
    }

    return render(request, 'taller/index.html', context)


def mecanicos_list(request):
    """
    Vista para listar todos los mecánicos.
    """
    mecanicos = Mecanico.objects.annotate(
        total_procedimientos=Count('procedimientos')
    ).order_by('nombre')

    # Filtro por especialidad
    especialidad = request.GET.get('especialidad')
    if especialidad:
        mecanicos = mecanicos.filter(especialidad__icontains=especialidad)

    # Filtro por estado activo
    activo = request.GET.get('activo')
    if activo:
        mecanicos = mecanicos.filter(activo=activo == 'true')

    # Paginación
    paginator = Paginator(mecanicos, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'especialidad': especialidad,
        'activo': activo,
    }

    return render(request, 'taller/mecanicos_list.html', context)


def mecanico_detail(request, pk):
    """
    Vista de detalle de un mecánico específico.
    """
    mecanico = get_object_or_404(Mecanico, pk=pk)
    procedimientos = mecanico.procedimientos.select_related('vehiculo').order_by('-fecha_inicio')

    # Paginación de procedimientos
    paginator = Paginator(procedimientos, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'mecanico': mecanico,
        'page_obj': page_obj,
    }

    return render(request, 'taller/mecanico_detail.html', context)


def vehiculos_list(request):
    """
    Vista para listar todos los vehículos.
    """
    vehiculos = Vehiculo.objects.annotate(
        total_procedimientos=Count('procedimientos')
    ).order_by('patente')

    # Filtro por año
    año = request.GET.get('año')
    if año:
        vehiculos = vehiculos.filter(año=año)

    # Búsqueda por patente o dueño
    search = request.GET.get('search')
    if search:
        vehiculos = vehiculos.filter(
            Q(patente__icontains=search) |
            Q(dueño__icontains=search) |
            Q(modelo__icontains=search)
        )

    # Paginación
    paginator = Paginator(vehiculos, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'año': año,
        'search': search,
    }

    return render(request, 'taller/vehiculos_list.html', context)


def vehiculo_detail(request, pk):
    """
    Vista de detalle de un vehículo específico.
    """
    vehiculo = get_object_or_404(Vehiculo, pk=pk)
    procedimientos = vehiculo.procedimientos.select_related('mecanico').order_by('-fecha_inicio')

    # Paginación de procedimientos
    paginator = Paginator(procedimientos, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'vehiculo': vehiculo,
        'page_obj': page_obj,
    }

    return render(request, 'taller/vehiculo_detail.html', context)


def procedimientos_list(request):
    """
    Vista para listar todos los procedimientos.
    """
    procedimientos = Procedimiento.objects.select_related(
        'mecanico', 'vehiculo'
    ).order_by('-fecha_inicio')

    # Filtro por estado
    completado = request.GET.get('completado')
    if completado:
        procedimientos = procedimientos.filter(completado=completado == 'true')

    # Filtro por mecánico
    mecanico_id = request.GET.get('mecanico')
    if mecanico_id:
        procedimientos = procedimientos.filter(mecanico_id=mecanico_id)

    # Búsqueda
    search = request.GET.get('search')
    if search:
        procedimientos = procedimientos.filter(
            Q(nombre__icontains=search) |
            Q(descripcion__icontains=search) |
            Q(vehiculo__patente__icontains=search)
        )

    # Paginación
    paginator = Paginator(procedimientos, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Para el filtro de mecánicos
    mecanicos = Mecanico.objects.filter(activo=True).order_by('nombre')

    context = {
        'page_obj': page_obj,
        'completado': completado,
        'mecanico_id': mecanico_id,
        'search': search,
        'mecanicos': mecanicos,
    }

    return render(request, 'taller/procedimientos_list.html', context)


def procedimiento_detail(request, pk):
    """
    Vista de detalle de un procedimiento específico.
    """
    procedimiento = get_object_or_404(
        Procedimiento.objects.select_related('mecanico', 'vehiculo'),
        pk=pk
    )

    context = {
        'procedimiento': procedimiento,
    }

    return render(request, 'taller/procedimiento_detail.html', context)
