from django.shortcuts import get_object_or_404
from django.http import JsonResponse
from django.db.models import Count, Q
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from .models import Mecanico, Vehiculo, Procedimiento
import json


@require_http_methods(["GET"])
def api_estadisticas(request):
    """
    API endpoint que devuelve estadísticas generales del taller.
    """
    # Estadísticas generales
    total_mecanicos = Mecanico.objects.filter(activo=True).count()
    total_vehiculos = Vehiculo.objects.count()
    procedimientos_activos = Procedimiento.objects.filter(completado=False).count()
    procedimientos_completados = Procedimiento.objects.filter(completado=True).count()

    # Procedimientos recientes
    procedimientos_recientes = Procedimiento.objects.select_related(
        'mecanico', 'vehiculo'
    ).order_by('-fecha_inicio')[:5]

    procedimientos_data = []
    for proc in procedimientos_recientes:
        procedimientos_data.append({
            'id': proc.id,
            'nombre': proc.nombre,
            'descripcion': proc.descripcion,
            'vehiculo': {
                'id': proc.vehiculo.id,
                'patente': proc.vehiculo.patente,
                'modelo': proc.vehiculo.modelo
            },
            'mecanico': {
                'id': proc.mecanico.id,
                'nombre': proc.mecanico.nombre,
                'especialidad': proc.mecanico.especialidad
            },
            'completado': proc.completado,
            'costo': float(proc.costo),
            'fecha_inicio': proc.fecha_inicio.isoformat()
        })

    data = {
        'estadisticas': {
            'total_mecanicos': total_mecanicos,
            'total_vehiculos': total_vehiculos,
            'procedimientos_activos': procedimientos_activos,
            'procedimientos_completados': procedimientos_completados,
        },
        'procedimientos_recientes': procedimientos_data
    }

    return JsonResponse(data)


@require_http_methods(["GET"])
def api_mecanicos_list(request):
    """
    API endpoint para listar todos los mecánicos con filtros.
    """
    mecanicos = Mecanico.objects.annotate(
        total_procedimientos=Count('procedimientos')
    ).order_by('nombre')

    # Filtro por especialidad
    especialidad = request.GET.get('especialidad')
    if especialidad:
        mecanicos = mecanicos.filter(especialidad__icontains=especialidad)

    # Filtro por estado activo
    activo = request.GET.get('activo')
    if activo:
        mecanicos = mecanicos.filter(activo=activo.lower() == 'true')

    # Paginación
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))
    paginator = Paginator(mecanicos, page_size)
    page_obj = paginator.get_page(page)

    mecanicos_data = []
    for mecanico in page_obj:
        mecanicos_data.append({
            'id': mecanico.id,
            'nombre': mecanico.nombre,
            'contacto': mecanico.contacto,
            'especialidad': mecanico.especialidad,
            'activo': mecanico.activo,
            'fecha_ingreso': mecanico.fecha_ingreso.isoformat(),
            'total_procedimientos': mecanico.total_procedimientos
        })

    data = {
        'mecanicos': mecanicos_data,
        'pagination': {
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
            'total_items': paginator.count,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous()
        },
        'filters': {
            'especialidad': especialidad,
            'activo': activo
        }
    }

    return JsonResponse(data)


@require_http_methods(["GET"])
def api_mecanico_detail(request, pk):
    """
    API endpoint para obtener detalles de un mecánico específico.
    """
    mecanico = get_object_or_404(Mecanico, pk=pk)
    procedimientos = mecanico.procedimientos.select_related('vehiculo').order_by('-fecha_inicio')

    # Paginación de procedimientos
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))
    paginator = Paginator(procedimientos, page_size)
    page_obj = paginator.get_page(page)

    procedimientos_data = []
    for proc in page_obj:
        procedimientos_data.append({
            'id': proc.id,
            'nombre': proc.nombre,
            'descripcion': proc.descripcion,
            'vehiculo': {
                'id': proc.vehiculo.id,
                'patente': proc.vehiculo.patente,
                'modelo': proc.vehiculo.modelo
            },
            'completado': proc.completado,
            'costo': float(proc.costo),
            'fecha_inicio': proc.fecha_inicio.isoformat(),
            'fecha_fin': proc.fecha_fin.isoformat() if proc.fecha_fin else None
        })

    data = {
        'mecanico': {
            'id': mecanico.id,
            'nombre': mecanico.nombre,
            'contacto': mecanico.contacto,
            'especialidad': mecanico.especialidad,
            'activo': mecanico.activo,
            'fecha_ingreso': mecanico.fecha_ingreso.isoformat()
        },
        'procedimientos': procedimientos_data,
        'pagination': {
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
            'total_items': paginator.count,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous()
        }
    }

    return JsonResponse(data)


@require_http_methods(["GET"])
def api_vehiculos_list(request):
    """
    API endpoint para listar todos los vehículos con filtros.
    """
    vehiculos = Vehiculo.objects.annotate(
        total_procedimientos=Count('procedimientos')
    ).order_by('patente')

    # Filtro por año
    año = request.GET.get('año')
    if año:
        try:
            vehiculos = vehiculos.filter(año=int(año))
        except ValueError:
            pass

    # Búsqueda por patente, dueño o modelo
    search = request.GET.get('search')
    if search:
        vehiculos = vehiculos.filter(
            Q(patente__icontains=search) |
            Q(dueño__icontains=search) |
            Q(modelo__icontains=search)
        )

    # Paginación
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))
    paginator = Paginator(vehiculos, page_size)
    page_obj = paginator.get_page(page)

    vehiculos_data = []
    for vehiculo in page_obj:
        vehiculos_data.append({
            'id': vehiculo.id,
            'patente': vehiculo.patente,
            'modelo': vehiculo.modelo,
            'año': vehiculo.año,
            'dueño': vehiculo.dueño,
            'fecha_registro': vehiculo.fecha_registro.isoformat(),
            'total_procedimientos': vehiculo.total_procedimientos
        })

    data = {
        'vehiculos': vehiculos_data,
        'pagination': {
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
            'total_items': paginator.count,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous()
        },
        'filters': {
            'año': año,
            'search': search
        }
    }

    return JsonResponse(data)


@require_http_methods(["GET"])
def api_vehiculo_detail(request, pk):
    """
    API endpoint para obtener detalles de un vehículo específico.
    """
    vehiculo = get_object_or_404(Vehiculo, pk=pk)
    procedimientos = vehiculo.procedimientos.select_related('mecanico').order_by('-fecha_inicio')

    # Paginación de procedimientos
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))
    paginator = Paginator(procedimientos, page_size)
    page_obj = paginator.get_page(page)

    procedimientos_data = []
    for proc in page_obj:
        procedimientos_data.append({
            'id': proc.id,
            'nombre': proc.nombre,
            'descripcion': proc.descripcion,
            'mecanico': {
                'id': proc.mecanico.id,
                'nombre': proc.mecanico.nombre,
                'especialidad': proc.mecanico.especialidad
            },
            'completado': proc.completado,
            'costo': float(proc.costo),
            'fecha_inicio': proc.fecha_inicio.isoformat(),
            'fecha_fin': proc.fecha_fin.isoformat() if proc.fecha_fin else None
        })

    data = {
        'vehiculo': {
            'id': vehiculo.id,
            'patente': vehiculo.patente,
            'modelo': vehiculo.modelo,
            'año': vehiculo.año,
            'dueño': vehiculo.dueño,
            'fecha_registro': vehiculo.fecha_registro.isoformat()
        },
        'procedimientos': procedimientos_data,
        'pagination': {
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
            'total_items': paginator.count,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous()
        }
    }

    return JsonResponse(data)


@require_http_methods(["GET"])
def api_procedimientos_list(request):
    """
    API endpoint para listar todos los procedimientos con filtros.
    """
    procedimientos = Procedimiento.objects.select_related(
        'mecanico', 'vehiculo'
    ).order_by('-fecha_inicio')

    # Filtro por estado
    completado = request.GET.get('completado')
    if completado:
        procedimientos = procedimientos.filter(completado=completado.lower() == 'true')

    # Filtro por mecánico
    mecanico_id = request.GET.get('mecanico')
    if mecanico_id:
        try:
            procedimientos = procedimientos.filter(mecanico_id=int(mecanico_id))
        except ValueError:
            pass

    # Búsqueda
    search = request.GET.get('search')
    if search:
        procedimientos = procedimientos.filter(
            Q(nombre__icontains=search) |
            Q(descripcion__icontains=search) |
            Q(vehiculo__patente__icontains=search)
        )

    # Paginación
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))
    paginator = Paginator(procedimientos, page_size)
    page_obj = paginator.get_page(page)

    procedimientos_data = []
    for proc in page_obj:
        procedimientos_data.append({
            'id': proc.id,
            'nombre': proc.nombre,
            'descripcion': proc.descripcion,
            'vehiculo': {
                'id': proc.vehiculo.id,
                'patente': proc.vehiculo.patente,
                'modelo': proc.vehiculo.modelo,
                'dueño': proc.vehiculo.dueño
            },
            'mecanico': {
                'id': proc.mecanico.id,
                'nombre': proc.mecanico.nombre,
                'especialidad': proc.mecanico.especialidad
            },
            'completado': proc.completado,
            'costo': float(proc.costo),
            'fecha_inicio': proc.fecha_inicio.isoformat(),
            'fecha_fin': proc.fecha_fin.isoformat() if proc.fecha_fin else None
        })

    # Lista de mecánicos para filtros
    mecanicos_data = []
    mecanicos = Mecanico.objects.filter(activo=True).order_by('nombre')
    for mec in mecanicos:
        mecanicos_data.append({
            'id': mec.id,
            'nombre': mec.nombre,
            'especialidad': mec.especialidad
        })

    data = {
        'procedimientos': procedimientos_data,
        'pagination': {
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
            'total_items': paginator.count,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous()
        },
        'filters': {
            'completado': completado,
            'mecanico_id': mecanico_id,
            'search': search
        },
        'mecanicos_disponibles': mecanicos_data
    }

    return JsonResponse(data)


@require_http_methods(["GET"])
def api_procedimiento_detail(request, pk):
    """
    API endpoint para obtener detalles de un procedimiento específico.
    """
    procedimiento = get_object_or_404(
        Procedimiento.objects.select_related('mecanico', 'vehiculo'),
        pk=pk
    )

    data = {
        'procedimiento': {
            'id': procedimiento.id,
            'nombre': procedimiento.nombre,
            'descripcion': procedimiento.descripcion,
            'completado': procedimiento.completado,
            'costo': float(procedimiento.costo),
            'fecha_inicio': procedimiento.fecha_inicio.isoformat(),
            'fecha_fin': procedimiento.fecha_fin.isoformat() if procedimiento.fecha_fin else None,
            'vehiculo': {
                'id': procedimiento.vehiculo.id,
                'patente': procedimiento.vehiculo.patente,
                'modelo': procedimiento.vehiculo.modelo,
                'año': procedimiento.vehiculo.año,
                'dueño': procedimiento.vehiculo.dueño
            },
            'mecanico': {
                'id': procedimiento.mecanico.id,
                'nombre': procedimiento.mecanico.nombre,
                'contacto': procedimiento.mecanico.contacto,
                'especialidad': procedimiento.mecanico.especialidad,
                'activo': procedimiento.mecanico.activo
            }
        }
    }

    return JsonResponse(data)
